# Live Translation Pipeline Setup Guide

## Prerequisites

### 1. Install Dependencies

```bash
# Install new dependencies
uv sync
```

### 2. Google Cloud Setup (Optional but Recommended)

For best STT and TTS quality, set up Google Cloud services:

#### Option A: Service Account (Recommended)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable APIs:
   - Cloud Speech-to-Text API
   - Cloud Text-to-Speech API
4. Create a service account:
   - Go to IAM & Admin > Service Accounts
   - Create service account with Speech and Text-to-Speech permissions
   - Download the JSON key file
5. Set environment variable:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
   ```

#### Option B: Use Gemini Fallback Only

If you don't want to set up Google Cloud, the system will automatically fall back to using Gemini for both STT and TTS.

### 3. Environment Variables

Create a `.env` file or set these environment variables:

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key

# Optional - for Google Cloud services
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Optional - for Cloudflare video uploads
CLOUDFLARE_API_TOKEN=your_token
CLOUDFLARE_ACCOUNT_ID=your_account_id

# Optional - for JWT authentication in chat
JWT_SECRET=your_jwt_secret
```

## Testing the Pipeline

### 1. Start the Server

```bash
# Development mode
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or using the config
python -c "import uvicorn; import config; uvicorn.run('main:app', host=config.SERVER_HOST, port=config.SERVER_PORT, reload=True)"
```

### 2. Test WebSocket Connection

You can test the WebSocket endpoint at: `ws://localhost:8000/ws/{target_language}`

Example target languages:

- `en` - English
- `uk` - Ukrainian
- `es` - Spanish
- `fr` - French
- `de` - German

### 3. Pipeline Configuration

You can configure the latency mode by modifying `config.py`:

```python
# Change this line for different modes:
PIPELINE_LATENCY_MODE = "low_latency"  # or "balanced" or "high_quality"
```

### 4. Test with a Simple Client

Create a test file `test_client.py`:

```python
import asyncio
import websockets
import json

async def test_translation():
    uri = "ws://localhost:8000/ws/uk"  # Translate to Ukrainian

    async with websockets.connect(uri) as websocket:
        # Send a test message
        await websocket.send(json.dumps({
            "type": "start_recording",
            "width": 1280,
            "height": 720
        }))

        # Listen for responses
        async for message in websocket:
            try:
                data = json.loads(message)
                print(f"Received: {data}")
            except:
                print(f"Received binary data: {len(message)} bytes")

if __name__ == "__main__":
    asyncio.run(test_translation())
```

### 5. Monitor Pipeline

Check pipeline status:

- GET `http://localhost:8000/api/pipeline/stats` - Overall statistics
- GET `http://localhost:8000/api/pipeline/sessions` - Active sessions
- GET `http://localhost:8000/docs` - API documentation

## Expected Message Flow

When testing, you should see these message types:

1. **Transcription messages** (from STT):

```json
{
  "type": "transcription",
  "text": "Hello world",
  "is_final": true,
  "confidence": 0.95,
  "timestamp": **********.123
}
```

2. **Translation messages**:

```json
{
  "type": "translation",
  "original_text": "Hello world",
  "translated_text": "Привіт світ",
  "target_language": "uk",
  "confidence": 0.9,
  "timestamp": **********.456
}
```

3. **Binary audio data** (from TTS) - Raw audio bytes

## Troubleshooting

### Common Issues

1. **Google Cloud STT/TTS not working**:

   - Check `GOOGLE_APPLICATION_CREDENTIALS` environment variable
   - Verify APIs are enabled in Google Cloud Console
   - Check service account permissions
   - System will automatically fall back to Gemini

2. **High latency**:

   - Try `PIPELINE_LATENCY_MODE = "low_latency"` in config
   - Check your internet connection
   - Monitor logs for bottlenecks

3. **Audio quality issues**:

   - Try `PIPELINE_LATENCY_MODE = "high_quality"` in config
   - Check audio sample rates match your input

4. **Memory issues**:
   - Reduce `MAX_CONCURRENT_SESSIONS` in config
   - Lower `TTS_MAX_CONCURRENT` setting

### Logs to Watch

The system logs all major events:

- STT transcription results
- Sentence detection and boundaries
- Translation requests and results
- TTS generation
- Pipeline statistics

Check logs for any errors or performance warnings.

### Performance Tuning

For different use cases:

**Real-time conversations** (low latency):

```python
PIPELINE_LATENCY_MODE = "low_latency"
SENTENCE_TIMEOUT_SECONDS = 2.0
MAX_CONTEXT_SENTENCES = 1
```

**Presentations** (high quality):

```python
PIPELINE_LATENCY_MODE = "high_quality"
SENTENCE_TIMEOUT_SECONDS = 5.0
MAX_CONTEXT_SENTENCES = 5
```

## Audio Input Requirements

The pipeline expects:

- **Sample Rate**: 16kHz (configurable in `config.INPUT_SAMPLE_RATE`)
- **Format**: 16-bit PCM
- **Channels**: Mono
- **Chunk Size**: 50-200ms recommended

Send audio as binary WebSocket messages.
