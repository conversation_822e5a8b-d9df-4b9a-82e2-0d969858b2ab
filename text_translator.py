"""
Text-to-text translation module with context awareness
"""
import asyncio
import logging
import time
from typing import Optional, Callable, List
from dataclasses import dataclass
from google import genai
from google.genai import types
import config
from sentence_buffer import TranslationContext, Sentence

logger = logging.getLogger(__name__)

@dataclass
class TranslationResult:
    original_text: str
    translated_text: str
    target_language: str
    confidence: float
    timestamp: float
    context_used: List[str]

class TextTranslator:
    """
    Handles text-to-text translation with contextual awareness
    """
    
    def __init__(self, target_language: str, source_language: str = "auto"):
        self.target_language = target_language
        self.source_language = source_language
        self.client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        self.translation_callback: Optional[Callable[[TranslationResult], None]] = None
        self.is_active = True
        
        # Translation quality settings
        self.use_context = True
        self.max_context_length = 500  # Characters
        
    def set_translation_callback(self, callback: Callable[[TranslationResult], None]):
        """Set callback function to receive translation results"""
        self.translation_callback = callback
        
    async def translate_with_context(self, context: TranslationContext) -> None:
        """Translate a sentence with contextual information"""
        if not self.is_active:
            return
            
        try:
            start_time = time.time()
            
            # Prepare context text
            context_text = self._prepare_context(context.previous_sentences)
            
            # Create translation prompt
            prompt = self._create_translation_prompt(
                context.current_sentence.text,
                context_text,
                self.target_language,
                self.source_language
            )
            
            logger.info(f"Translating: '{context.current_sentence.text}' to {self.target_language}")
            
            # Perform translation
            response = await self._call_gemini_translation(prompt)
            
            if response and response.text:
                translated_text = self._extract_translation(response.text)
                
                translation_result = TranslationResult(
                    original_text=context.current_sentence.text,
                    translated_text=translated_text,
                    target_language=self.target_language,
                    confidence=self._estimate_confidence(context.current_sentence, translated_text),
                    timestamp=time.time(),
                    context_used=[s.text for s in context.previous_sentences]
                )
                
                # Call callback
                if self.translation_callback:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.translation_callback, translation_result
                    )
                
                elapsed_time = time.time() - start_time
                logger.info(f"Translation completed in {elapsed_time:.2f}s: '{translated_text}'")
            else:
                logger.warning("Empty translation response from Gemini")
                
        except Exception as e:
            logger.error(f"Error in translation: {e}")
            
            # Create fallback result
            fallback_result = TranslationResult(
                original_text=context.current_sentence.text,
                translated_text=f"[Translation Error: {context.current_sentence.text}]",
                target_language=self.target_language,
                confidence=0.0,
                timestamp=time.time(),
                context_used=[]
            )
            
            if self.translation_callback:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.translation_callback, fallback_result
                )
    
    def _prepare_context(self, previous_sentences: List[Sentence]) -> str:
        """Prepare context text from previous sentences"""
        if not self.use_context or not previous_sentences:
            return ""
            
        # Join previous sentences, respecting max context length
        context_parts = []
        total_length = 0
        
        for sentence in reversed(previous_sentences):
            if total_length + len(sentence.text) > self.max_context_length:
                break
            context_parts.insert(0, sentence.text)
            total_length += len(sentence.text)
        
        return " ".join(context_parts)
    
    def _create_translation_prompt(self, text: str, context: str, target_lang: str, source_lang: str) -> str:
        """Create a comprehensive translation prompt"""
        
        # Language code mapping for better prompts
        lang_names = {
            "en": "English",
            "uk": "Ukrainian", 
            "de": "German",
            "fr": "French",
            "es": "Spanish",
            "it": "Italian",
            "pt": "Portuguese",
            "ru": "Russian",
            "ja": "Japanese",
            "ko": "Korean",
            "zh": "Chinese",
            "ar": "Arabic"
        }
        
        target_lang_name = lang_names.get(target_lang, target_lang)
        source_lang_name = lang_names.get(source_lang, "the source language") if source_lang != "auto" else "the source language"
        
        if context:
            prompt = f"""You are a professional real-time translator. Translate the following text from {source_lang_name} to {target_lang_name}.

CONTEXT (previous sentences for reference):
{context}

TRANSLATE THIS SENTENCE:
{text}

INSTRUCTIONS:
1. Provide ONLY the translation, no explanations or additional text
2. Maintain the natural flow and context from previous sentences
3. Preserve the original meaning and tone
4. Use appropriate formality level
5. If the text is incomplete or unclear, translate what you can understand
6. For proper nouns, keep them as-is unless there's a standard translation

Translation:"""
        else:
            prompt = f"""You are a professional real-time translator. Translate the following text from {source_lang_name} to {target_lang_name}.

TRANSLATE THIS SENTENCE:
{text}

INSTRUCTIONS:
1. Provide ONLY the translation, no explanations or additional text
2. Maintain natural and fluent expression
3. Preserve the original meaning and tone
4. Use appropriate formality level
5. If the text is incomplete or unclear, translate what you can understand
6. For proper nouns, keep them as-is unless there's a standard translation

Translation:"""
        
        return prompt
    
    async def _call_gemini_translation(self, prompt: str) -> Optional[types.GenerateContentResponse]:
        """Call Gemini API for translation"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model="gemini-1.5-flash",
                    contents=[types.Content(parts=[types.Part(text=prompt)])],
                    config=types.GenerateContentConfig(
                        temperature=0.1,  # Low temperature for consistent translations
                        top_p=0.8,
                        candidate_count=1,
                        max_output_tokens=200,
                    )
                )
            )
            return response
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            return None
    
    def _extract_translation(self, response_text: str) -> str:
        """Extract clean translation from Gemini response"""
        # Remove common prefixes/suffixes that Gemini might add
        text = response_text.strip()
        
        # Remove common unwanted patterns
        patterns_to_remove = [
            r"^Translation:\s*",
            r"^Here's the translation:\s*",
            r"^The translation is:\s*",
            r"^\*\*Translation:\*\*\s*",
            r"^[\"']|[\"']$",  # Remove quotes at start/end
        ]
        
        import re
        for pattern in patterns_to_remove:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def _estimate_confidence(self, original_sentence: Sentence, translated_text: str) -> float:
        """Estimate translation confidence based on various factors"""
        base_confidence = original_sentence.confidence
        
        # Adjust based on translation length ratio
        length_ratio = len(translated_text) / max(len(original_sentence.text), 1)
        if 0.5 <= length_ratio <= 2.0:  # Reasonable length ratio
            length_bonus = 0.1
        else:
            length_bonus = -0.1
            
        # Adjust based on sentence completeness
        completeness_bonus = 0.1 if original_sentence.is_complete else -0.2
        
        # Calculate final confidence
        estimated_confidence = min(1.0, max(0.0, base_confidence + length_bonus + completeness_bonus))
        
        return estimated_confidence
    
    async def translate_simple(self, text: str) -> Optional[str]:
        """Simple translation without context (for fallback use)"""
        try:
            prompt = f"Translate this text to {self.target_language}: {text}"
            response = await self._call_gemini_translation(prompt)
            
            if response and response.text:
                return self._extract_translation(response.text)
            return None
            
        except Exception as e:
            logger.error(f"Error in simple translation: {e}")
            return None
    
    def stop(self):
        """Stop the translator"""
        self.is_active = False
        logger.info("Text translator stopped")

class BatchTextTranslator:
    """
    Handles batch translation for better efficiency when multiple sentences are ready
    """
    
    def __init__(self, target_language: str, source_language: str = "auto", batch_size: int = 3):
        self.target_language = target_language
        self.source_language = source_language
        self.batch_size = batch_size
        self.client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        self.pending_translations = []
        self.translation_callback: Optional[Callable[[List[TranslationResult]], None]] = None
        
    def set_translation_callback(self, callback: Callable[[List[TranslationResult]], None]):
        """Set callback function to receive batch translation results"""
        self.translation_callback = callback
        
    async def add_for_translation(self, context: TranslationContext) -> None:
        """Add a sentence context for batch translation"""
        self.pending_translations.append(context)
        
        # Process batch if we have enough items
        if len(self.pending_translations) >= self.batch_size:
            await self._process_batch()
    
    async def flush_batch(self) -> None:
        """Process any pending translations immediately"""
        if self.pending_translations:
            await self._process_batch()
            
    async def _process_batch(self) -> None:
        """Process a batch of translations"""
        if not self.pending_translations:
            return
            
        try:
            batch = self.pending_translations.copy()
            self.pending_translations.clear()
            
            # Create batch prompt
            prompt = self._create_batch_prompt(batch)
            
            # Perform batch translation
            response = await self._call_gemini_translation(prompt)
            
            if response and response.text:
                translations = self._parse_batch_response(response.text, batch)
                
                if self.translation_callback:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.translation_callback, translations
                    )
            
        except Exception as e:
            logger.error(f"Error in batch translation: {e}")
            
    def _create_batch_prompt(self, contexts: List[TranslationContext]) -> str:
        """Create prompt for batch translation"""
        lang_names = {
            "en": "English", "uk": "Ukrainian", "de": "German", "fr": "French",
            "es": "Spanish", "it": "Italian", "pt": "Portuguese", "ru": "Russian",
            "ja": "Japanese", "ko": "Korean", "zh": "Chinese", "ar": "Arabic"
        }
        
        target_lang_name = lang_names.get(self.target_language, self.target_language)
        
        prompt = f"Translate these sentences to {target_lang_name}. Return only the translations, one per line:\n\n"
        
        for i, context in enumerate(contexts, 1):
            prompt += f"{i}. {context.current_sentence.text}\n"
            
        return prompt
        
    def _parse_batch_response(self, response_text: str, contexts: List[TranslationContext]) -> List[TranslationResult]:
        """Parse batch translation response"""
        lines = response_text.strip().split('\n')
        results = []
        
        for i, context in enumerate(contexts):
            if i < len(lines):
                translated_text = lines[i].strip()
                # Remove numbering if present
                import re
                translated_text = re.sub(r'^\d+\.\s*', '', translated_text)
            else:
                translated_text = f"[Translation missing for: {context.current_sentence.text}]"
                
            result = TranslationResult(
                original_text=context.current_sentence.text,
                translated_text=translated_text,
                target_language=self.target_language,
                confidence=context.current_sentence.confidence,
                timestamp=time.time(),
                context_used=[s.text for s in context.previous_sentences]
            )
            results.append(result)
            
        return results
        
    async def _call_gemini_translation(self, prompt: str) -> Optional[types.GenerateContentResponse]:
        """Call Gemini API for translation"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model="gemini-1.5-flash",
                    contents=[types.Content(parts=[types.Part(text=prompt)])],
                    config=types.GenerateContentConfig(
                        temperature=0.1,
                        top_p=0.8,
                        candidate_count=1,
                        max_output_tokens=500,
                    )
                )
            )
            return response
        except Exception as e:
            logger.error(f"Gemini API error in batch translation: {e}")
            return None 