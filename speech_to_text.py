"""
Speech-to-Text module for live audio transcription
"""
import asyncio
import logging
import threading
import queue
from typing import Generator, Optional, Callable
from google.cloud import speech
import wave
import io
from dataclasses import dataclass
import time
import config

logger = logging.getLogger(__name__)

@dataclass
class TranscriptionResult:
    text: str
    is_final: bool
    confidence: float
    timestamp: float
    language: str

class SpeechToText:
    """
    Real-time speech-to-text transcription using Google Cloud Speech-to-Text
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000):
        self.source_language = source_language
        self.sample_rate = sample_rate
        try:
            self.client = speech.SpeechClient()
            self.is_available = True
            logger.info("Google Cloud STT client initialized successfully")
        except Exception as e:
            logger.warning(f"Google Cloud STT not available: {e}")
            self.client = None
            self.is_available = False
            
        self.is_streaming = False
        self.audio_queue = queue.Queue()
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.streaming_thread = None
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        self.transcription_callback = callback
        
    async def start_streaming_transcription(self) -> None:
        """Start streaming transcription"""
        if self.is_streaming or not self.is_available:
            return
            
        self.is_streaming = True
        logger.info("Starting streaming speech-to-text transcription")
        
        # Start the streaming thread
        self.streaming_thread = threading.Thread(target=self._streaming_worker)
        self.streaming_thread.daemon = True
        self.streaming_thread.start()
        
    def _streaming_worker(self):
        """Worker thread for Google Cloud STT streaming"""
        try:
            # Configure streaming recognition
            config_obj = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=self.sample_rate,
                language_code=self.source_language if self.source_language != "auto" else "en-US",
                enable_automatic_punctuation=True,
                enable_word_time_offsets=False,
                model="latest_long",
                use_enhanced=True,
            )
            
            streaming_config = speech.StreamingRecognitionConfig(
                config=config_obj,
                interim_results=True,
                single_utterance=False,
            )
            
            # Create the streaming recognition request
            audio_generator = self._audio_generator()
            requests = (speech.StreamingRecognizeRequest(audio_content=chunk)
                       for chunk in audio_generator)
            
            # Start streaming recognition
            responses = self.client.streaming_recognize(streaming_config, requests)
            
            # Process responses
            for response in responses:
                if not self.is_streaming:
                    break
                    
                if not response.results:
                    continue
                    
                result = response.results[0]
                if not result.alternatives:
                    continue
                    
                alternative = result.alternatives[0]
                
                transcription_result = TranscriptionResult(
                    text=alternative.transcript,
                    is_final=result.is_final,
                    confidence=alternative.confidence if hasattr(alternative, 'confidence') else 0.0,
                    timestamp=time.time(),
                    language=self.source_language
                )
                
                # Schedule callback in the event loop
                if self.transcription_callback:
                    try:
                        # Get the running event loop and schedule the callback
                        loop = asyncio.get_running_loop()
                        asyncio.run_coroutine_threadsafe(
                            self._call_transcription_callback(transcription_result),
                            loop
                        )
                    except RuntimeError:
                        # No event loop running, call directly
                        self.transcription_callback(transcription_result)
                        
        except Exception as e:
            logger.error(f"Error in streaming transcription worker: {e}")
        finally:
            self.is_streaming = False
            
    async def _call_transcription_callback(self, transcription_result: TranscriptionResult):
        """Helper to call transcription callback in async context"""
        if self.transcription_callback:
            await asyncio.get_event_loop().run_in_executor(
                None, self.transcription_callback, transcription_result
            )
            
    def _audio_generator(self) -> Generator[bytes, None, None]:
        """Generate audio chunks for streaming (synchronous generator)"""
        chunk_size = int(self.sample_rate * 0.1)  # 100ms chunks
        
        while self.is_streaming:
            try:
                # Get audio chunk from queue with timeout
                chunk = self.audio_queue.get(timeout=0.1)
                if chunk is None:  # Sentinel value to stop
                    break
                yield chunk
            except queue.Empty:
                continue
                
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the transcription buffer"""
        if self.is_streaming:
            try:
                self.audio_queue.put_nowait(audio_data)
            except queue.Full:
                logger.warning("Audio queue full, dropping chunk")
                
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription"""
        self.is_streaming = False
        
        # Send sentinel value to stop the generator
        try:
            self.audio_queue.put_nowait(None)
        except queue.Full:
            pass
            
        # Wait for streaming thread to finish
        if self.streaming_thread and self.streaming_thread.is_alive():
            self.streaming_thread.join(timeout=2.0)
            
        # Clear the queue
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except queue.Empty:
                break
                
        logger.info("Stopped streaming speech-to-text transcription")

class FallbackSpeechToText:
    """
    Fallback STT implementation using Gemini for when Google Cloud STT is not available
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000):
        self.source_language = source_language
        self.sample_rate = sample_rate
        self.is_streaming = False
        self.audio_buffer = bytearray()
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.accumulated_audio = bytearray()
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        self.transcription_callback = callback
        
    async def start_streaming_transcription(self) -> None:
        """Start streaming transcription using Gemini"""
        if self.is_streaming:
            return
            
        self.is_streaming = True
        logger.info("Starting fallback streaming speech-to-text using Gemini")
        
        # Start processing task
        asyncio.create_task(self._process_audio_chunks())
        
    async def _process_audio_chunks(self):
        """Process audio chunks periodically for transcription"""
        from google import genai
        from google.genai import types
        
        client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        
        while self.is_streaming:
            await asyncio.sleep(2.0)  # Process every 2 seconds
            
            if len(self.accumulated_audio) < self.sample_rate * 1:  # At least 1 second of audio
                continue
                
            try:
                # Convert audio to WAV format
                audio_data = bytes(self.accumulated_audio)
                wav_buffer = io.BytesIO()
                
                with wave.open(wav_buffer, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    wav_file.writeframes(audio_data)
                
                wav_data = wav_buffer.getvalue()
                
                # Use Gemini for transcription
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: client.models.generate_content(
                        model="gemini-1.5-flash",
                        contents=[
                            types.Content(parts=[
                                types.Part(text="Transcribe this audio to text. Only return the transcribed text, no other commentary."),
                                types.Part(inline_data=types.Blob(data=wav_data, mime_type="audio/wav"))
                            ])
                        ]
                    )
                )
                
                if response and response.text:
                    transcription_result = TranscriptionResult(
                        text=response.text.strip(),
                        is_final=True,
                        confidence=0.8,  # Estimated confidence
                        timestamp=time.time(),
                        language=self.source_language
                    )
                    
                    if self.transcription_callback:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.transcription_callback, transcription_result
                        )
                
                # Clear processed audio
                self.accumulated_audio.clear()
                
            except Exception as e:
                logger.error(f"Error in fallback transcription: {e}")
                
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the transcription buffer"""
        if self.is_streaming:
            self.accumulated_audio.extend(audio_data)
            
            # Keep only last 10 seconds to prevent memory issues
            max_samples = self.sample_rate * 2 * 10  # 10 seconds of 16-bit audio
            if len(self.accumulated_audio) > max_samples:
                self.accumulated_audio = self.accumulated_audio[-max_samples:]
                
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription"""
        self.is_streaming = False
        self.accumulated_audio.clear()
        logger.info("Stopped fallback streaming speech-to-text transcription") 