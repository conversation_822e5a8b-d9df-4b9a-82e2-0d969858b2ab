"""
Sentence detection and buffering system for live translation
"""
import asyncio
import logging
import re
import time
from typing import List, Optional, Callable, Deque
from collections import deque
from dataclasses import dataclass
from speech_to_text import TranscriptionResult

logger = logging.getLogger(__name__)

@dataclass
class Sentence:
    text: str
    timestamp: float
    confidence: float
    language: str
    is_complete: bool = True

@dataclass
class TranslationContext:
    previous_sentences: List[Sentence]
    current_sentence: Sentence
    max_context_sentences: int = 3

class SentenceBuffer:
    """
    Manages sentence detection, buffering, and context for translation
    """
    
    def __init__(self, max_context_sentences: int = 3, sentence_timeout: float = 3.0):
        self.max_context_sentences = max_context_sentences
        self.sentence_timeout = sentence_timeout
        
        # Buffers
        self.current_text = ""
        self.interim_text = ""
        self.completed_sentences: Deque[Sentence] = deque(maxlen=max_context_sentences * 2)
        self.pending_sentence_start_time = None
        
        # Callbacks
        self.sentence_ready_callback: Optional[Callable[[TranslationContext], None]] = None
        
        # Sentence detection patterns
        self.sentence_end_pattern = re.compile(r'[.!?]+\s*')
        self.abbreviation_pattern = re.compile(r'\b(?:Mr|Mrs|Ms|Dr|Prof|Sr|Jr|vs|etc|i\.e|e\.g)\.\s*', re.IGNORECASE)
        
        # Timing
        self.last_activity_time = time.time()
        
        # Start timeout checker
        asyncio.create_task(self._timeout_checker())
        
    def set_sentence_ready_callback(self, callback: Callable[[TranslationContext], None]):
        """Set callback function to receive completed sentences with context"""
        self.sentence_ready_callback = callback
        
    async def add_transcription(self, transcription: TranscriptionResult) -> None:
        """Add new transcription result to the buffer"""
        self.last_activity_time = time.time()
        
        if transcription.is_final:
            # Final transcription - update current text
            self.current_text = transcription.text.strip()
            self.interim_text = ""
            
            # Check if we have complete sentences
            await self._process_final_text(transcription)
        else:
            # Interim transcription - store temporarily
            self.interim_text = transcription.text.strip()
            
            # If we haven't started a sentence timer yet, start it
            if self.pending_sentence_start_time is None and self.current_text:
                self.pending_sentence_start_time = time.time()
    
    async def _process_final_text(self, transcription: TranscriptionResult) -> None:
        """Process final transcription text and extract sentences"""
        if not self.current_text:
            return
            
        # Split text into potential sentences
        sentences = self._extract_sentences(self.current_text)
        
        if sentences:
            # Process each complete sentence
            for sentence_text in sentences[:-1]:  # All but the last
                if sentence_text.strip():
                    await self._emit_sentence(sentence_text.strip(), transcription)
            
            # Check if the last part is a complete sentence
            last_part = sentences[-1].strip()
            if last_part and self._is_complete_sentence(last_part):
                await self._emit_sentence(last_part, transcription)
                self.current_text = ""
            else:
                # Keep the incomplete part for next time
                self.current_text = last_part
        else:
            # No sentence boundaries found, check timeout
            if self.pending_sentence_start_time is None:
                self.pending_sentence_start_time = time.time()
    
    def _extract_sentences(self, text: str) -> List[str]:
        """Extract sentences from text, handling abbreviations"""
        # First, protect abbreviations
        protected_text = text
        abbreviations = list(self.abbreviation_pattern.finditer(text))
        
        # Replace abbreviation periods temporarily
        for match in reversed(abbreviations):
            start, end = match.span()
            protected_text = protected_text[:end-1] + "@@PERIOD@@" + protected_text[end:]
        
        # Split on sentence endings
        parts = self.sentence_end_pattern.split(protected_text)
        
        # Restore abbreviation periods
        restored_parts = []
        for part in parts:
            restored_part = part.replace("@@PERIOD@@", ".")
            restored_parts.append(restored_part)
        
        return [part for part in restored_parts if part.strip()]
    
    def _is_complete_sentence(self, text: str) -> bool:
        """Check if text appears to be a complete sentence"""
        text = text.strip()
        if not text:
            return False
            
        # Check for sentence ending punctuation
        if re.search(r'[.!?]+$', text):
            return True
            
        # Check for minimum length and some heuristics
        if len(text) < 3:
            return False
            
        # Check if it looks like a complete thought (has subject and verb indicators)
        words = text.lower().split()
        if len(words) >= 3:
            # Simple heuristic: if it has reasonable length and no trailing comma/conjunction
            if not text.endswith(',') and not text.lower().endswith(('and', 'or', 'but', 'so', 'yet')):
                return True
                
        return False
    
    async def _emit_sentence(self, sentence_text: str, transcription: TranscriptionResult) -> None:
        """Emit a completed sentence with context"""
        sentence = Sentence(
            text=sentence_text,
            timestamp=transcription.timestamp,
            confidence=transcription.confidence,
            language=transcription.language,
            is_complete=True
        )
        
        # Add to completed sentences buffer
        self.completed_sentences.append(sentence)
        
        # Create translation context
        context = TranslationContext(
            previous_sentences=list(self.completed_sentences)[-self.max_context_sentences-1:-1],
            current_sentence=sentence,
            max_context_sentences=self.max_context_sentences
        )
        
        # Emit to callback
        if self.sentence_ready_callback:
            await asyncio.get_event_loop().run_in_executor(
                None, self.sentence_ready_callback, context
            )
        
        # Reset pending sentence timer
        self.pending_sentence_start_time = None
        
        logger.info(f"Emitted sentence: '{sentence_text}' with {len(context.previous_sentences)} context sentences")
    
    async def _timeout_checker(self) -> None:
        """Check for sentence timeouts and emit incomplete sentences"""
        while True:
            await asyncio.sleep(0.5)  # Check every 500ms
            
            current_time = time.time()
            
            # Check if we have pending text that's been waiting too long
            if (self.pending_sentence_start_time and 
                current_time - self.pending_sentence_start_time > self.sentence_timeout):
                
                # Force emit the current text as a sentence
                if self.current_text.strip():
                    sentence = Sentence(
                        text=self.current_text.strip(),
                        timestamp=current_time,
                        confidence=0.7,  # Lower confidence for timeout sentences
                        language="auto",
                        is_complete=False  # Mark as incomplete
                    )
                    
                    # Add to completed sentences buffer
                    self.completed_sentences.append(sentence)
                    
                    # Create translation context
                    context = TranslationContext(
                        previous_sentences=list(self.completed_sentences)[-self.max_context_sentences-1:-1],
                        current_sentence=sentence,
                        max_context_sentences=self.max_context_sentences
                    )
                    
                    # Emit to callback
                    if self.sentence_ready_callback:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.sentence_ready_callback, context
                        )
                    
                    logger.info(f"Timeout-emitted incomplete sentence: '{self.current_text.strip()}'")
                    
                    # Clear current text and reset timer
                    self.current_text = ""
                    self.pending_sentence_start_time = None
    
    async def force_flush(self) -> None:
        """Force emit any pending text as a sentence"""
        if self.current_text.strip():
            sentence = Sentence(
                text=self.current_text.strip(),
                timestamp=time.time(),
                confidence=0.8,
                language="auto",
                is_complete=False
            )
            
            # Add to completed sentences buffer
            self.completed_sentences.append(sentence)
            
            # Create translation context
            context = TranslationContext(
                previous_sentences=list(self.completed_sentences)[-self.max_context_sentences-1:-1],
                current_sentence=sentence,
                max_context_sentences=self.max_context_sentences
            )
            
            # Emit to callback
            if self.sentence_ready_callback:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.sentence_ready_callback, context
                )
            
            logger.info(f"Force-flushed sentence: '{self.current_text.strip()}'")
            
            # Clear current text and reset timer
            self.current_text = ""
            self.pending_sentence_start_time = None
    
    def get_recent_context(self, max_sentences: int = None) -> List[Sentence]:
        """Get recent sentences for context"""
        if max_sentences is None:
            max_sentences = self.max_context_sentences
        return list(self.completed_sentences)[-max_sentences:]
    
    def clear_buffer(self) -> None:
        """Clear all buffers"""
        self.current_text = ""
        self.interim_text = ""
        self.completed_sentences.clear()
        self.pending_sentence_start_time = None
        logger.info("Sentence buffer cleared") 