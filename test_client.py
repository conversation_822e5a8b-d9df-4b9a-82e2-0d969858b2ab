    #!/usr/bin/env python3
"""
Simple test client for the live translation pipeline
"""
import asyncio
import websockets
import json
import wave
import time
import os
from pathlib import Path

async def test_basic_connection():
    """Test basic WebSocket connection"""
    uri = "ws://localhost:8001/ws/en"  # Translate to Ukrainian
    print(f"Connecting to {uri}...")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✓ Connected successfully!")
            
            # Send start recording message
            await websocket.send(json.dumps({
                "type": "start_recording",
                "width": 1280,
                "height": 720
            }))
            print("✓ Sent start recording message")
            
            # Listen for initial responses (should get connection confirmation)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                if isinstance(response, str):
                    data = json.loads(response)
                    print(f"✓ Received JSON response: {data}")
                else:
                    print(f"✓ Received binary data: {len(response)} bytes")
            except asyncio.TimeoutError:
                print("⚠ No immediate response (this is normal)")
            
            # Send end of turn
            await websocket.send(json.dumps({"type": "end_of_turn"}))
            print("✓ Sent end_of_turn message")
            
            print("✓ Basic connection test passed!")
            
    except Exception as e:
        print(f"✗ Connection failed: {e}")
        return False
    
    return True

async def test_with_sample_audio():
    """Test with a sample audio file if available"""
    # Look for sample audio files
    sample_files = [
        "sample.wav",
        "artemdemo.wav", 
        "d1.wav",
        "d2.wav"
    ]
    
    sample_file = None
    for filename in sample_files:
        if Path(filename).exists():
            sample_file = filename
            break
    
    if not sample_file:
        print("⚠ No sample audio files found. Creating a silent test audio...")
        # Create a short silent audio file for testing
        sample_file = "test_audio.wav"
        create_test_audio(sample_file)
    
    print(f"Using audio file: {sample_file}")
    
    uri = "ws://localhost:8000/ws/en"  # Translate to English
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✓ Connected for audio test")
            
            # Send start recording
            await websocket.send(json.dumps({
                "type": "start_recording",
                "width": 1280,
                "height": 720
            }))
            
            # Send audio data
            await send_audio_file(websocket, sample_file)
            
            # Send end of turn
            await websocket.send(json.dumps({"type": "end_of_turn"}))
            
            # Listen for responses
            print("Listening for pipeline responses...")
            response_count = 0
            start_time = time.time()
            
            while time.time() - start_time < 30 and response_count < 10:  # Max 30 seconds or 10 responses
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_count += 1
                    
                    if isinstance(response, str):
                        data = json.loads(response)
                        msg_type = data.get('type', 'unknown')
                        
                        if msg_type == 'transcription':
                            print(f"📝 Transcription: '{data.get('text')}' (final: {data.get('is_final')})")
                        elif msg_type == 'translation':
                            print(f"🔄 Translation: '{data.get('original_text')}' → '{data.get('translated_text')}'")
                        else:
                            print(f"📨 JSON: {data}")
                    else:
                        print(f"🔊 Audio: {len(response)} bytes")
                        
                except asyncio.TimeoutError:
                    print("⏱ Timeout waiting for response")
                    break
                except Exception as e:
                    print(f"Error receiving: {e}")
                    break
            
            print(f"✓ Audio test completed. Received {response_count} responses.")
            
    except Exception as e:
        print(f"✗ Audio test failed: {e}")
        return False
    
    return True

async def send_audio_file(websocket, filename):
    """Send audio file in chunks"""
    try:
        with wave.open(filename, 'rb') as wf:
            print(f"📁 Audio file info:")
            print(f"   Sample rate: {wf.getframerate()} Hz")
            print(f"   Channels: {wf.getnchannels()}")
            print(f"   Sample width: {wf.getsampwidth()} bytes")
            print(f"   Duration: {wf.getnframes() / wf.getframerate():.1f}s")
            
            # Read and send in chunks
            chunk_size = 1024  # Small chunks for testing
            chunk_count = 0
            
            while True:
                chunk = wf.readframes(chunk_size)
                if not chunk:
                    break
                
                await websocket.send(chunk)
                chunk_count += 1
                
                # Small delay to simulate real-time streaming
                await asyncio.sleep(0.05)  # 50ms delay
            
            print(f"✓ Sent {chunk_count} audio chunks")
            
    except Exception as e:
        print(f"✗ Error sending audio: {e}")

def create_test_audio(filename, duration_seconds=3):
    """Create a test audio file with silence"""
    import numpy as np
    
    sample_rate = 16000
    samples = int(sample_rate * duration_seconds)
    
    # Create silent audio (you could add tone or noise here)
    audio_data = np.zeros(samples, dtype=np.int16)
    
    with wave.open(filename, 'wb') as wf:
        wf.setnchannels(1)  # Mono
        wf.setsampwidth(2)  # 16-bit
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data.tobytes())
    
    print(f"✓ Created test audio file: {filename}")

async def test_pipeline_endpoints():
    """Test the REST API endpoints"""
    import aiohttp
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test stats endpoint
        try:
            async with session.get(f"{base_url}/api/pipeline/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ Pipeline stats: {data}")
                else:
                    print(f"⚠ Stats endpoint returned: {response.status}")
        except Exception as e:
            print(f"✗ Stats endpoint error: {e}")
        
        # Test sessions endpoint  
        try:
            async with session.get(f"{base_url}/api/pipeline/sessions") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ Active sessions: {data}")
                else:
                    print(f"⚠ Sessions endpoint returned: {response.status}")
        except Exception as e:
            print(f"✗ Sessions endpoint error: {e}")

async def main():
    """Run all tests"""
    print("🚀 Testing Live Translation Pipeline")
    print("=" * 50)
    
    # Test 1: Basic connection
    print("\n1. Testing basic WebSocket connection...")
    basic_ok = await test_basic_connection()
    
    # Test 2: REST endpoints
    print("\n2. Testing REST API endpoints...")
    await test_pipeline_endpoints()
    
    # Test 3: Audio pipeline (if basic connection works)
    if basic_ok:
        print("\n3. Testing audio pipeline...")
        await test_with_sample_audio()
    else:
        print("\n3. Skipping audio test (basic connection failed)")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")
    print("\nIf you see errors:")
    print("- Make sure the server is running: uvicorn main:app --reload")
    print("- Check the setup guide in setup_guide.md")
    print("- Look at server logs for detailed error messages")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc() 